#!/bin/bash

# Cloudera QuickStart VM Setup Script
# This script helps you set up the Cloudera QuickStart VM for your Big Data Analytics project

echo "=== Cloudera QuickStart VM Setup ==="
echo ""

# Check if VirtualBox is installed
if ! command -v VBoxManage &> /dev/null; then
    echo "❌ VirtualBox is not installed or not in PATH"
    echo "Please install VirtualBox from: https://www.virtualbox.org/wiki/Downloads"
    exit 1
fi

echo "✅ VirtualBox found"

# Check if the OVF file exists
OVF_FILE="cloudera-quickstart-vm-5.4.2-0-virtualbox/cloudera-quickstart-vm-5.4.2-0-virtualbox.ovf"
if [ ! -f "$OVF_FILE" ]; then
    echo "❌ Cloudera OVF file not found: $OVF_FILE"
    exit 1
fi

echo "✅ Cloudera OVF file found"

# Import the VM
echo ""
echo "🚀 Importing Cloudera QuickStart VM..."
echo "This may take several minutes..."

VM_NAME="Cloudera-QuickStart-VM"

# Import the OVF
VBoxManage import "$OVF_FILE" --vsys 0 --vmname "$VM_NAME"

if [ $? -eq 0 ]; then
    echo "✅ VM imported successfully as '$VM_NAME'"
    
    # Configure VM settings for better performance
    echo ""
    echo "⚙️  Configuring VM settings..."
    
    # Allocate more memory (4GB recommended, adjust based on your system)
    VBoxManage modifyvm "$VM_NAME" --memory 4096
    
    # Enable virtualization features
    VBoxManage modifyvm "$VM_NAME" --ioapic on
    VBoxManage modifyvm "$VM_NAME" --pae on
    
    # Set CPU count (adjust based on your system)
    VBoxManage modifyvm "$VM_NAME" --cpus 2
    
    echo "✅ VM configuration completed"
    
    echo ""
    echo "🎉 Setup completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Start the VM: VBoxManage startvm '$VM_NAME'"
    echo "2. Or open VirtualBox GUI and start '$VM_NAME'"
    echo "3. Login credentials:"
    echo "   - Username: cloudera"
    echo "   - Password: cloudera"
    echo ""
    echo "4. Once logged in, you can access:"
    echo "   - Cloudera Manager: http://localhost:7180"
    echo "   - Hue (Hadoop UI): http://localhost:8888"
    echo ""
    
else
    echo "❌ Failed to import VM"
    exit 1
fi
