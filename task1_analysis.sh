#!/bin/bash

# Task 1: Big Data Analysis of Frankenstein Novel
# This script contains commands to run inside the Cloudera VM

echo "=== Task 1: Frankenstein Text Analysis ==="
echo ""

# Step 1: Prepare the data
echo "📁 Step 1: Preparing Frankenstein text data..."

# Create a directory for our project
mkdir -p ~/frankenstein_analysis
cd ~/frankenstein_analysis

# Copy the Frankenstein text (you'll need to transfer this file to the VM)
echo "Please ensure Frank.txt is copied to ~/frankenstein_analysis/"
echo ""

# Step 2: Upload data to HDFS
echo "📤 Step 2: Uploading data to HDFS..."

# Create HDFS directory
hdfs dfs -mkdir -p /user/cloudera/frankenstein

# Upload the text file to HDFS
hdfs dfs -put Frank.txt /user/cloudera/frankenstein/

# Verify upload
hdfs dfs -ls /user/cloudera/frankenstein/

echo ""

# Step 3: Basic word count analysis using MapReduce
echo "🔍 Step 3: Running word count analysis..."

# Run Hadoop word count example
hadoop jar /usr/lib/hadoop-mapreduce/hadoop-mapreduce-examples.jar wordcount \
    /user/cloudera/frankenstein/Frank.txt \
    /user/cloudera/frankenstein/wordcount_output

# View results
echo "Top 20 most frequent words:"
hdfs dfs -cat /user/cloudera/frankenstein/wordcount_output/part-r-00000 | sort -k2 -nr | head -20

echo ""

# Step 4: Create Hive table for analysis
echo "🗃️  Step 4: Creating Hive table..."

# Start Hive and create database
hive -e "
CREATE DATABASE IF NOT EXISTS frankenstein_db;
USE frankenstein_db;

-- Create external table for the text
CREATE EXTERNAL TABLE IF NOT EXISTS frankenstein_text (
    line STRING
)
STORED AS TEXTFILE
LOCATION '/user/cloudera/frankenstein/';

-- Show table info
DESCRIBE frankenstein_text;

-- Count total lines
SELECT COUNT(*) as total_lines FROM frankenstein_text;
"

echo ""

# Step 5: Advanced text analysis queries
echo "📊 Step 5: Advanced text analysis..."

hive -e "
USE frankenstein_db;

-- Word frequency analysis
CREATE TABLE word_frequency AS
SELECT 
    word,
    COUNT(*) as frequency
FROM (
    SELECT explode(split(lower(regexp_replace(line, '[^a-zA-Z\\s]', '')), '\\s+')) as word
    FROM frankenstein_text
    WHERE length(trim(line)) > 0
) words
WHERE length(word) > 2
GROUP BY word
ORDER BY frequency DESC
LIMIT 50;

-- Show results
SELECT * FROM word_frequency LIMIT 20;
"

echo ""
echo "✅ Task 1 analysis completed!"
echo ""
echo "Results are stored in:"
echo "- HDFS: /user/cloudera/frankenstein/"
echo "- Hive database: frankenstein_db"
echo ""
echo "You can access Hue at http://localhost:8888 to view results graphically"
