#!/usr/bin/env python3
"""
Data preparation script for Frankenstein text analysis
This script cleans and prepares the text data for Hadoop processing
"""

import re
import os

def clean_frankenstein_text():
    """Clean and prepare the Frankenstein text for analysis"""
    
    input_file = "Frank.txt"
    output_file = "Frank_cleaned.txt"
    
    if not os.path.exists(input_file):
        print(f"❌ Error: {input_file} not found!")
        return False
    
    print(f"📖 Reading {input_file}...")
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"📊 Original file size: {len(content):,} characters")
    
    # Find the start of the actual story (after Project Gutenberg header)
    story_start = content.find("*** START OF THE PROJECT GUTENBERG EBOOK")
    if story_start != -1:
        # Find the end of the header
        story_start = content.find("***", story_start + 1) + 3
        content = content[story_start:]
    
    # Find the end of the story (before Project Gutenberg footer)
    story_end = content.find("*** END OF THE PROJECT GUTENBERG EBOOK")
    if story_end != -1:
        content = content[:story_end]
    
    # Clean the text
    lines = content.split('\n')
    cleaned_lines = []
    
    for line in lines:
        line = line.strip()
        
        # Skip empty lines and very short lines
        if len(line) < 3:
            continue
            
        # Skip lines that are just chapter headers or page numbers
        if re.match(r'^(Chapter|Letter)\s+\d+$', line):
            continue
            
        # Skip lines with just underscores or special characters
        if re.match(r'^[_\-=\s]*$', line):
            continue
            
        cleaned_lines.append(line)
    
    # Join lines back together
    cleaned_content = '\n'.join(cleaned_lines)
    
    print(f"📊 Cleaned file size: {len(cleaned_content):,} characters")
    print(f"📊 Number of lines: {len(cleaned_lines):,}")
    
    # Write cleaned content
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(cleaned_content)
    
    print(f"✅ Cleaned text saved to {output_file}")
    
    # Generate basic statistics
    words = cleaned_content.lower().split()
    word_count = len(words)
    unique_words = len(set(words))
    
    print(f"\n📈 Text Statistics:")
    print(f"   Total words: {word_count:,}")
    print(f"   Unique words: {unique_words:,}")
    print(f"   Average words per line: {word_count/len(cleaned_lines):.1f}")
    
    return True

def create_sample_data():
    """Create a smaller sample for testing"""
    
    input_file = "Frank_cleaned.txt"
    sample_file = "Frank_sample.txt"
    
    if not os.path.exists(input_file):
        print(f"❌ Error: {input_file} not found! Run clean_frankenstein_text() first.")
        return False
    
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Take first 1000 lines for testing
    sample_lines = lines[:1000]
    
    with open(sample_file, 'w', encoding='utf-8') as f:
        f.writelines(sample_lines)
    
    print(f"✅ Sample data (1000 lines) saved to {sample_file}")
    return True

def analyze_text_structure():
    """Analyze the structure of the text"""
    
    input_file = "Frank.txt"
    
    if not os.path.exists(input_file):
        print(f"❌ Error: {input_file} not found!")
        return
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find chapters and letters
    chapters = re.findall(r'Chapter \d+', content, re.IGNORECASE)
    letters = re.findall(r'Letter \d+', content, re.IGNORECASE)
    
    print(f"📚 Text Structure Analysis:")
    print(f"   Letters found: {len(letters)}")
    print(f"   Chapters found: {len(chapters)}")
    
    if letters:
        print(f"   Letters: {', '.join(letters[:5])}{'...' if len(letters) > 5 else ''}")
    
    if chapters:
        print(f"   Chapters: {', '.join(chapters[:10])}{'...' if len(chapters) > 10 else ''}")

if __name__ == "__main__":
    print("=== Frankenstein Text Data Preparation ===\n")
    
    # Analyze original structure
    analyze_text_structure()
    print()
    
    # Clean the text
    if clean_frankenstein_text():
        # Create sample for testing
        create_sample_data()
    
    print("\n🎉 Data preparation completed!")
    print("\nFiles created:")
    print("- Frank_cleaned.txt (for main analysis)")
    print("- Frank_sample.txt (for testing)")
    print("\nNext steps:")
    print("1. Start Cloudera VM")
    print("2. Transfer these files to the VM")
    print("3. Run task1_analysis.sh")
