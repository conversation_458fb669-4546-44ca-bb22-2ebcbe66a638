# Big Data Analytics Project - Cloudera Setup and Task 1 Guide

## Project Overview
This project involves analyzing <PERSON>'s "Frankenstein" novel using Big Data tools in the Cloudera ecosystem.

## Files in Your Project
- `Frank.txt` - Complete text of Frankenstein novel (448KB)
- `cloudera-quickstart-vm-5.4.2-0-virtualbox/` - Cloudera QuickStart VM files
- `Project_DSC302_2504.pdf` - Project requirements (9 pages)
- `setup_cloudera.sh` - VM setup script
- `task1_analysis.sh` - Analysis commands for Task 1

## Prerequisites
1. **VirtualBox** installed on your system
2. At least **8GB RAM** (4GB will be allocated to VM)
3. **20GB free disk space**
4. **Virtualization enabled** in BIOS

## Step-by-Step Setup

### 1. Install VirtualBox (if not already installed)
```bash
# macOS (using Homebrew)
brew install --cask virtualbox

# Or download from: https://www.virtualbox.org/wiki/Downloads
```

### 2. Set Up Cloudera VM
```bash
# Make the setup script executable
chmod +x setup_cloudera.sh

# Run the setup script
./setup_cloudera.sh
```

### 3. Start the VM
```bash
# Option 1: Command line
VBoxManage startvm "Cloudera-QuickStart-VM"

# Option 2: Use VirtualBox GUI
# Open VirtualBox → Select "Cloudera-QuickStart-VM" → Start
```

### 4. Login to VM
- **Username:** `cloudera`
- **Password:** `cloudera`

### 5. Transfer Frank.txt to VM
```bash
# Option 1: Use shared folders (recommended)
# In VirtualBox: Devices → Shared Folders → Add your project folder

# Option 2: Use SCP (if SSH is enabled)
scp Frank.txt cloudera@VM_IP:~/frankenstein_analysis/
```

### 6. Run Task 1 Analysis
```bash
# Inside the VM terminal
chmod +x task1_analysis.sh
./task1_analysis.sh
```

## What Task 1 Likely Involves

Based on typical Big Data Analytics projects, Task 1 probably includes:

### 1. **Data Ingestion**
- Load Frankenstein text into HDFS
- Verify data integrity and structure

### 2. **Basic Text Analysis**
- Word count using MapReduce
- Character frequency analysis
- Line count and basic statistics

### 3. **Hive/SQL Analysis**
- Create Hive tables for structured querying
- Word frequency analysis
- Text pattern identification

### 4. **Visualization**
- Use Hue for data visualization
- Create charts showing word frequencies
- Generate basic statistics reports

## Key Cloudera Services

### Hadoop Ecosystem Components:
- **HDFS** - Distributed file system
- **MapReduce** - Distributed processing
- **Hive** - SQL-like queries on big data
- **Pig** - High-level data flow language
- **HBase** - NoSQL database
- **Spark** - Fast cluster computing

### Web Interfaces:
- **Cloudera Manager:** http://localhost:7180
- **Hue (Hadoop UI):** http://localhost:8888
- **HDFS NameNode:** http://localhost:50070
- **ResourceManager:** http://localhost:8088

## Common Commands

### HDFS Commands:
```bash
# List files
hdfs dfs -ls /

# Create directory
hdfs dfs -mkdir /user/cloudera/data

# Upload file
hdfs dfs -put localfile.txt /user/cloudera/data/

# Download file
hdfs dfs -get /user/cloudera/data/file.txt ./

# View file content
hdfs dfs -cat /user/cloudera/data/file.txt
```

### Hive Commands:
```sql
-- Create database
CREATE DATABASE project_db;

-- Use database
USE project_db;

-- Create table
CREATE TABLE text_data (line STRING);

-- Load data
LOAD DATA INPATH '/user/cloudera/data/file.txt' INTO TABLE text_data;
```

## Troubleshooting

### VM Won't Start:
1. Check virtualization is enabled in BIOS
2. Ensure sufficient RAM available
3. Try reducing VM memory allocation

### Services Not Starting:
```bash
# Start Cloudera services
sudo service cloudera-scm-server start
sudo service cloudera-scm-agent start
```

### HDFS Issues:
```bash
# Format namenode (only if necessary)
sudo -u hdfs hdfs namenode -format
```

## Expected Deliverables for Task 1

1. **Data Loading Report**
   - Screenshots of successful HDFS upload
   - File size and structure verification

2. **Word Count Analysis**
   - Top 20 most frequent words
   - Total word count
   - Unique word count

3. **Hive Query Results**
   - SQL queries used
   - Result tables/screenshots

4. **Performance Metrics**
   - Job execution times
   - Resource utilization

## Next Steps

After completing Task 1:
1. Document your findings
2. Take screenshots of results
3. Prepare for subsequent tasks
4. Consider advanced analytics (sentiment analysis, character networks, etc.)

## Support Resources

- [Cloudera Documentation](https://docs.cloudera.com/)
- [Hadoop Documentation](https://hadoop.apache.org/docs/)
- [Hive Language Manual](https://cwiki.apache.org/confluence/display/Hive/LanguageManual)
