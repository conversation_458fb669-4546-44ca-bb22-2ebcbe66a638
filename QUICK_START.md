# 🚀 Quick Start Guide - Cloudera Setup & Task 1

## ✅ What's Ready
Your project workspace is now prepared with:

### 📁 Data Files:
- `<PERSON>.txt` - Original Frankenstein novel (449KB)
- `Frank_cleaned.txt` - Cleaned version for analysis (420KB)
- `Frank_sample.txt` - Sample data for testing (66KB)

### 🛠️ Setup Scripts:
- `setup_cloudera.sh` - Automated VM import and configuration
- `task1_analysis.sh` - Complete Task 1 analysis commands
- `prepare_data.py` - Data cleaning and preparation

### 📖 Documentation:
- `README_Setup_Guide.md` - Comprehensive setup guide
- `QUICK_START.md` - This quick start guide

## 🎯 Text Analysis Summary
**Frankenstein Novel Statistics:**
- **Total words:** 74,930
- **Unique words:** 11,200
- **Lines:** 6,363
- **Structure:** 4 Letters + 24 Chapters

## ⚡ Immediate Next Steps

### 1. Start Cloudera VM Setup
```bash
# Run the automated setup
./setup_cloudera.sh
```

### 2. Start the VM
```bash
# Option A: Command line
VBoxManage startvm "Cloudera-QuickStart-VM"

# Option B: Use VirtualBox GUI
# Open VirtualBox → Select VM → Start
```

### 3. Login to VM
- **Username:** `cloudera`
- **Password:** `cloudera`

### 4. Transfer Files to VM
```bash
# Create project directory in VM
mkdir -p ~/frankenstein_analysis

# Copy files (use one of these methods):
# Method 1: Shared folder (recommended)
# Method 2: SCP if network is configured
# Method 3: Copy-paste if guest additions installed
```

### 5. Run Task 1 Analysis
```bash
# Inside the VM
cd ~/frankenstein_analysis
chmod +x task1_analysis.sh
./task1_analysis.sh
```

## 🔍 Expected Task 1 Results

### 1. **HDFS Data Loading**
- Frankenstein text uploaded to `/user/cloudera/frankenstein/`
- File verification and size confirmation

### 2. **MapReduce Word Count**
- Top 20 most frequent words
- Total word count: ~74,930 words
- Processing time and job statistics

### 3. **Hive Analysis**
- Database: `frankenstein_db`
- Table: `frankenstein_text`
- Word frequency analysis
- Text statistics queries

### 4. **Web Interface Access**
- **Hue:** http://localhost:8888
- **Cloudera Manager:** http://localhost:7180
- **HDFS NameNode:** http://localhost:50070

## 🎯 Likely Task 1 Requirements

Based on typical Big Data Analytics projects:

### **Part A: Environment Setup (20%)**
- ✅ Cloudera VM installation and configuration
- ✅ HDFS setup and data loading
- ✅ Service verification

### **Part B: Basic Analysis (40%)**
- Word count using MapReduce
- Character frequency analysis
- Text statistics (lines, words, characters)

### **Part C: Advanced Queries (30%)**
- Hive table creation and queries
- Word frequency ranking
- Pattern analysis (e.g., character names, themes)

### **Part D: Documentation (10%)**
- Screenshots of results
- Query documentation
- Performance metrics

## 🔧 Troubleshooting

### VM Issues:
```bash
# If VM won't start
VBoxManage list vms
VBoxManage showvminfo "Cloudera-QuickStart-VM"

# Check system resources
free -h
df -h
```

### Hadoop Issues:
```bash
# Check HDFS status
hdfs dfsadmin -report

# Restart services if needed
sudo service hadoop-hdfs-namenode restart
sudo service hadoop-hdfs-datanode restart
```

### File Transfer Issues:
```bash
# Check if file exists
ls -la Frank_cleaned.txt

# Verify HDFS upload
hdfs dfs -ls /user/cloudera/frankenstein/
hdfs dfs -du -h /user/cloudera/frankenstein/
```

## 📊 Sample Expected Output

### Word Count Top 10:
```
the     4,567
and     3,234
of      2,891
to      2,456
a       2,123
in      1,987
that    1,654
was     1,432
his     1,298
he      1,156
```

### Hive Query Example:
```sql
-- Most frequent words (excluding common words)
SELECT word, frequency 
FROM word_frequency 
WHERE word NOT IN ('the','and','of','to','a','in','that','was','his','he')
ORDER BY frequency DESC 
LIMIT 10;
```

## 🎉 Success Indicators

You'll know Task 1 is complete when you have:

1. ✅ VM running with all services active
2. ✅ Frankenstein text loaded in HDFS
3. ✅ MapReduce word count completed
4. ✅ Hive database and tables created
5. ✅ Word frequency analysis results
6. ✅ Screenshots and documentation

## 📞 Need Help?

If you encounter issues:
1. Check the detailed `README_Setup_Guide.md`
2. Verify all prerequisites are met
3. Check VM resource allocation
4. Ensure virtualization is enabled
5. Try the sample data first (`Frank_sample.txt`)

**Good luck with your Big Data Analytics project! 🚀**
